import os
import json
from typing import Dict, List
from collections import Counter

CONFIG = {
    # ====== 🚀 核心配置 ======
    'dataset': 'CoNLL2003',
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',
    'reranker_model': 'BAAI/bge-reranker-v2-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'http://8.138.94.162:3001/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen2.5-7B-Instruct',
    'api_key': 'sk-zhongyushi',
    'max_concurrent_requests': 500,  # 保持高并发能力
    'batch_size': 64,  # 批处理大小：SiliconFlow最大允许64个请求
    'embedding_batch_size': 64,  # 嵌入API批处理大小：SiliconFlow嵌入API限制
    'batch_delay': 1.0,  # 批处理延迟：每批之间间隔1秒
    'timeout_seconds': 600,  # API超时时间：10分钟，支持大规模处理
    'api_timeout': 300,  # 单个API调用超时：5分钟，避免检索timeout
    'max_retries': 3,  # 重试次数：减少重试避免过长等待
    'retry_delay': 5,  # 重试延迟：5秒，给API服务器更多恢复时间

    # ====== 📊 界面配置 ======
    'log_level': 'WARNING',
    'show_progress': True,
    'progress_desc': '🧠 Processing NER',

    # ====== 📁 路径配置 ======
    'vector_cache_dir': './cache/vector',
    'vector_db_path': './cache/chromadb',
    'data_root_dir': './data',

    # ====== 🧠 元认知配置 ======
    'meta_cognitive_config': {
        'difficulty_analysis_temperature': 0.1,
        'requirement_generation_temperature': 0.2,
        'structured_conversion_temperature': 0.0,
        'max_difficulties': 5,
        'max_requirements': 3,
    },

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
        'fallback_examples_count': 2,
    },

    # ====== 🤖 LangChain配置 ======
    'langchain_config': {
        'enable_langchain': True,
        'enable_langsmith': False,
        'enable_fallback': True,
    },

    # ====== 🎯 提示词模板 ======
    'system_prompt_template': """You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below:
{label_set_info}

### Context and Examples ###
{examples_str}

### Text to Analyze ###
{user_query}

OUTPUT FORMAT: Return a JSON object where keys are entity types and values are arrays of entity strings.
- If entities are found: {output_format_example}
- If no entities are found: {{}}
- Only include entity types that have found entities
- Do not include empty arrays

OUTPUT: Valid JSON only. No explanations.""",

    # ====== 📊 数据集配置 ======
    'current_dataset': 'ace2005',
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}

# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available


# ====== 🧠 智能标签集检测 ======

def detect_dataset_labels(dataset_path: str, sample_size: int = 100) -> Dict[str, int]:
    """
    智能检测数据集中的标签集

    Args:
        dataset_path: 数据集文件路径
        sample_size: 采样数量，用于分析

    Returns:
        Dict[str, int]: 标签及其出现频次
    """
    if not os.path.exists(dataset_path):
        return {}

    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 限制采样数量以提高性能
        if len(data) > sample_size:
            data = data[:sample_size]

        label_counter = Counter()

        for item in data:
            labels = item.get('label', {})
            if isinstance(labels, dict):
                for label_type, entities in labels.items():
                    if isinstance(entities, list) and entities:
                        label_counter[label_type] += len(entities)

        return dict(label_counter)

    except (json.JSONDecodeError, FileNotFoundError, KeyError) as e:
        print(f"⚠️ 检测标签集失败 {dataset_path}: {e}")
        return {}


def auto_detect_and_update_labels():
    """
    自动检测数据集中的实际标签并更新配置
    使用数据驱动的方式，以实际数据为准
    """
    print("🔍 开始智能检测数据集标签集...")

    for dataset_key, dataset_info in CONFIG['datasets'].items():
        dataset_path = dataset_info['path']
        print(f"📊 检测数据集: {dataset_info['name']}")

        detected_labels = detect_dataset_labels(dataset_path)

        if detected_labels:
            # 按频次排序，取最常见的标签
            sorted_labels = sorted(detected_labels.items(), key=lambda x: x[1], reverse=True)
            detected_label_names = [label for label, _ in sorted_labels]

            # 直接使用检测到的标签更新配置
            CONFIG['datasets'][dataset_key]['labels'] = detected_label_names
            CONFIG['datasets'][dataset_key]['detected_labels'] = detected_labels

            print(f"  ✅ 检测到 {len(detected_label_names)} 个标签: {detected_label_names}")

            # 根据检测到的标签重新生成label_prompt
            label_descriptions = generate_label_descriptions(detected_label_names)
            CONFIG['datasets'][dataset_key]['label_prompt'] = label_descriptions
        else:
            print("  ❌ 未检测到标签")

    print("🎉 标签集检测完成！")


def generate_label_descriptions(labels: List[str]) -> str:
    """
    根据标签名称生成描述性提示

    Args:
        labels: 标签列表

    Returns:
        str: 格式化的标签描述
    """
    # 标签描述映射
    label_desc_map = {
        # CoNLL 2003 格式
        'PER': 'Person names',
        'ORG': 'Organizations',
        'LOC': 'Locations',
        'MISC': 'Miscellaneous entities',

        # ACE 2005 格式
        'person': 'Person names',
        'organization': 'Organizations',
        'location': 'Locations',
        'geographical-social-political': 'Geographic and political entities',
        'weapon': 'Weapons',
        'facility': 'Facilities',
        'vehicle': 'Vehicles',

        # 通用格式
        'date': 'Dates and times',
        'money': 'Monetary values',
        'percent': 'Percentages',
        'time': 'Time expressions',
        'cardinal': 'Cardinal numbers',
        'ordinal': 'Ordinal numbers'
    }

    descriptions = []
    for label in labels:
        desc = label_desc_map.get(label, f"{label.title()} entities")
        descriptions.append(f"- {label}: {desc}")

    return "Entity types:\n" + "\n".join(descriptions)


def get_current_labels() -> List[str]:
    """获取当前数据集的标签列表"""
    current_dataset = get_current_dataset_info()
    return current_dataset.get('labels', [])


def get_optimized_label_prompt() -> str:
    """获取优化后的标签提示"""
    labels = get_current_labels()

    if not labels:
        return "Entity types: No specific entity types defined."

    return generate_label_descriptions(labels)


def get_current_output_format_example() -> str:
    """根据当前数据集生成正确的输出格式示例"""
    current_dataset = get_current_dataset_info()
    labels = current_dataset.get('labels', [])

    if not labels:
        return '{}'

    # 为每个标签生成示例
    examples = {}
    for label in labels[:3]:  # 只取前3个标签作为示例
        if label.lower() in ['person', 'per']:
            examples[label] = ["John", "Mary"]
        elif label.lower() in ['organization', 'org']:
            examples[label] = ["Apple Inc."]
        elif label.lower() in ['location', 'loc']:
            examples[label] = ["New York"]
        else:
            # 为其他标签生成通用示例
            examples[label] = ["example"]

    import json
    return json.dumps(examples)


def initialize_datasets():
    """
    初始化数据集配置，自动检测标签集
    在程序启动时调用一次
    """
    print("🚀 初始化数据集配置...")

    # 检查是否需要更新标签集
    need_update = False
    for dataset_key, dataset_info in CONFIG['datasets'].items():
        if 'detected_labels' not in dataset_info:
            need_update = True
            break

    if need_update:
        auto_detect_and_update_labels()
    else:
        print("✅ 数据集标签集已是最新状态")



